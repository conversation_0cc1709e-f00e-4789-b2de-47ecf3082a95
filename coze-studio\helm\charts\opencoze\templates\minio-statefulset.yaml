{{- if .Values.minio.enabled }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "opencoze.fullname" . }}-minio
  labels:
    {{- include "opencoze.labels" . | nindent 4 }}
spec:
  serviceName: {{ .Release.Name }}-minio
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: minio
      app.kubernetes.io/instance: {{ .Release.Name }}
      app.kubernetes.io/name: {{ include "opencoze.name" . }}
  template:
    metadata:
      labels:
        app.kubernetes.io/component: minio
        app.kubernetes.io/instance: {{ .Release.Name }}
        app.kubernetes.io/name: {{ include "opencoze.name" . }}
    spec:
      containers:
        - name: minio
          image: "{{ .Values.minio.image.repository }}:{{ .Values.minio.image.tag }}"
          args:
            - server
            - /data
            - --console-address
            - ":{{ .Values.minio.service.consolePort }}"
          env:
            - name: MINIO_ROOT_USER
              value: {{ .Values.minio.accessKey | quote }}
            - name: MINIO_ROOT_PASSWORD
              value: {{ .Values.minio.secretKey | quote }}
            - name: MINIO_DEFAULT_BUCKETS
              value: "{{ .Values.minio.bucket }},{{ .Values.milvus.bucketName }}"
          ports:
            - containerPort: {{ .Values.minio.service.port }}
            - containerPort: {{ .Values.minio.service.consolePort }}
          volumeMounts:
            - name: minio-data
              mountPath: /data
          livenessProbe:
            httpGet:
              path: /minio/health/live
              port: {{ .Values.minio.service.port }}
            initialDelaySeconds: 30
            periodSeconds: 15
          readinessProbe:
            httpGet:
              path: /minio/health/ready
              port: {{ .Values.minio.service.port }}
            initialDelaySeconds: 20
            periodSeconds: 10
  volumeClaimTemplates:
    - metadata:
        name: minio-data
      spec:
        accessModes: [ "ReadWriteOnce" ]
      {{- if .Values.minio.persistence.storageClassName }}
        storageClassName: {{ .Values.minio.persistence.storageClassName | quote }}
      {{- end }}
        resources:
          requests:
            storage: {{ .Values.minio.persistence.size | quote }}
{{- end }}