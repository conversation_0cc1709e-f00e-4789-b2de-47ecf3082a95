/* stylelint-disable custom-property-pattern */
/* stylelint-disable declaration-no-important */
body {
  .result {
    overflow: hidden scroll;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
    align-self: stretch;

    width: 100%;
    height: 104px !important;
    padding: 6px 12px;

    background: var(--Light-usage-disabled---color-disabled-fill, rgba(46, 46, 56, 4%));
    border: 1px solid var(--Light-usage-border---color-border, rgba(29, 28, 35, 8%));
    border-radius: 8px;
  }

  .is-fetching {
    color: var(--Light-usage-text---color-text-3, rgba(29, 28, 35, 35%));
  }
}
