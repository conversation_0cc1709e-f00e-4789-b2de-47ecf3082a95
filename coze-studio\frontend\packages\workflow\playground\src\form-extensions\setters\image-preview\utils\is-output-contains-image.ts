/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { FlowNodeFormData } from '@flowgram-adapter/free-layout-editor';
import { type FlowNodeEntity } from '@flowgram-adapter/free-layout-editor';
import { ViewVariableType } from '@coze-workflow/base';

function isContainsImage(output): boolean {
  if (!output) {
    return false;
  }

  const checkValue = (_value): boolean => {
    if (
      [
        ViewVariableType.Image,
        ViewVariableType.ArrayImage,
        ViewVariableType.Svg,
        ViewVariableType.ArraySvg,
      ].includes(_value?.type || '')
    ) {
      return true;
    }

    return _value?.children?.some(checkValue) ?? false;
  };

  return Array.isArray(output) ? output.some(checkValue) : checkValue(output);
}

export function isOutputsContainsImage(node: FlowNodeEntity): boolean {
  const formModel = node?.getData<FlowNodeFormData>(FlowNodeFormData).formModel;
  const outputs = formModel?.getFormItemValueByPath?.('/outputs');

  return isContainsImage(outputs);
}
