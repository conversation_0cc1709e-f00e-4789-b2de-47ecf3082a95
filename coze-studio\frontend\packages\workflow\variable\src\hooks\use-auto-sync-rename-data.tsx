/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable @typescript-eslint/no-explicit-any */

import { useEffect, useRef } from 'react';

import { VariableFieldKeyRenameService } from '@flowgram-adapter/free-layout-editor';
import { useService } from '@flowgram-adapter/free-layout-editor';

import { traverseUpdateRefExpressionByRename } from '../core/utils/traverse-refs';

export function useAutoSyncRenameData(
  data: any,
  ctx: {
    onDataRenamed?: (_newData?: any) => void;
  } = {},
) {
  const { onDataRenamed } = ctx || {};
  const fieldRenameService: VariableFieldKeyRenameService = useService(
    VariableFieldKeyRenameService,
  );

  const latest = useRef(data);
  latest.current = data;

  useEffect(() => {
    const disposable = fieldRenameService.onRename(({ before, after }) => {
      traverseUpdateRefExpressionByRename(
        latest.current,
        { before, after },
        {
          onDataRenamed,
        },
      );
    });

    return () => disposable.dispose();
  }, []);
}
