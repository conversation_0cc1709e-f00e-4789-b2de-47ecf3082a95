2025/08/03-05:09:21.842855 7 RocksDB version: 6.29.5
2025/08/03-05:09:21.849089 7 Git sha 0
2025/08/03-05:09:21.849106 7 Compile date 2024-11-15 11:22:58
2025/08/03-05:09:21.849121 7 DB SUMMARY
2025/08/03-05:09:21.849123 7 DB Session ID:  0IG7HY073V2R4E4TSRW7
2025/08/03-05:09:21.854911 7 CURRENT file:  CURRENT
2025/08/03-05:09:21.854944 7 IDENTITY file:  IDENTITY
2025/08/03-05:09:21.858483 7 MANIFEST file:  MANIFEST-000004 size: 59 Bytes
2025/08/03-05:09:21.858508 7 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 0, files: 
2025/08/03-05:09:21.858522 7 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000005.log size: 1742 ; 
2025/08/03-05:09:21.858530 7                         Options.error_if_exists: 0
2025/08/03-05:09:21.858541 7                       Options.create_if_missing: 1
2025/08/03-05:09:21.858543 7                         Options.paranoid_checks: 1
2025/08/03-05:09:21.858545 7             Options.flush_verify_memtable_count: 1
2025/08/03-05:09:21.858635 7                               Options.track_and_verify_wals_in_manifest: 0
2025/08/03-05:09:21.858652 7                                     Options.env: 0x7f13b8261d00
2025/08/03-05:09:21.858654 7                                      Options.fs: PosixFileSystem
2025/08/03-05:09:21.858657 7                                Options.info_log: 0x7f13afb8afa0
2025/08/03-05:09:21.858658 7                Options.max_file_opening_threads: 16
2025/08/03-05:09:21.858660 7                              Options.statistics: (nil)
2025/08/03-05:09:21.858662 7                               Options.use_fsync: 0
2025/08/03-05:09:21.858664 7                       Options.max_log_file_size: 0
2025/08/03-05:09:21.858666 7                  Options.max_manifest_file_size: 1073741824
2025/08/03-05:09:21.858668 7                   Options.log_file_time_to_roll: 0
2025/08/03-05:09:21.858669 7                       Options.keep_log_file_num: 1000
2025/08/03-05:09:21.858671 7                    Options.recycle_log_file_num: 0
2025/08/03-05:09:21.858672 7                         Options.allow_fallocate: 1
2025/08/03-05:09:21.858673 7                        Options.allow_mmap_reads: 0
2025/08/03-05:09:21.858675 7                       Options.allow_mmap_writes: 0
2025/08/03-05:09:21.858677 7                        Options.use_direct_reads: 0
2025/08/03-05:09:21.858678 7                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/03-05:09:21.858679 7          Options.create_missing_column_families: 0
2025/08/03-05:09:21.858681 7                              Options.db_log_dir: 
2025/08/03-05:09:21.858682 7                                 Options.wal_dir: 
2025/08/03-05:09:21.858684 7                Options.table_cache_numshardbits: 6
2025/08/03-05:09:21.858685 7                         Options.WAL_ttl_seconds: 0
2025/08/03-05:09:21.858687 7                       Options.WAL_size_limit_MB: 0
2025/08/03-05:09:21.858688 7                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/03-05:09:21.858725 7             Options.manifest_preallocation_size: 4194304
2025/08/03-05:09:21.858732 7                     Options.is_fd_close_on_exec: 1
2025/08/03-05:09:21.858778 7                   Options.advise_random_on_open: 1
2025/08/03-05:09:21.858801 7                   Options.experimental_mempurge_threshold: 0.000000
2025/08/03-05:09:21.858820 7                    Options.db_write_buffer_size: 0
2025/08/03-05:09:21.858823 7                    Options.write_buffer_manager: 0x7f13aff74100
2025/08/03-05:09:21.858824 7         Options.access_hint_on_compaction_start: 1
2025/08/03-05:09:21.858826 7  Options.new_table_reader_for_compaction_inputs: 0
2025/08/03-05:09:21.858828 7           Options.random_access_max_buffer_size: 1048576
2025/08/03-05:09:21.858830 7                      Options.use_adaptive_mutex: 0
2025/08/03-05:09:21.858832 7                            Options.rate_limiter: (nil)
2025/08/03-05:09:21.859078 7     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/03-05:09:21.859107 7                       Options.wal_recovery_mode: 2
2025/08/03-05:09:21.859110 7                  Options.enable_thread_tracking: 0
2025/08/03-05:09:21.859857 7                  Options.enable_pipelined_write: 0
2025/08/03-05:09:21.859863 7                  Options.unordered_write: 0
2025/08/03-05:09:21.859865 7         Options.allow_concurrent_memtable_write: 1
2025/08/03-05:09:21.859867 7      Options.enable_write_thread_adaptive_yield: 1
2025/08/03-05:09:21.859869 7             Options.write_thread_max_yield_usec: 100
2025/08/03-05:09:21.859870 7            Options.write_thread_slow_yield_usec: 3
2025/08/03-05:09:21.859872 7                               Options.row_cache: None
2025/08/03-05:09:21.859874 7                              Options.wal_filter: None
2025/08/03-05:09:21.859876 7             Options.avoid_flush_during_recovery: 0
2025/08/03-05:09:21.859878 7             Options.allow_ingest_behind: 0
2025/08/03-05:09:21.859879 7             Options.preserve_deletes: 0
2025/08/03-05:09:21.859881 7             Options.two_write_queues: 0
2025/08/03-05:09:21.859883 7             Options.manual_wal_flush: 0
2025/08/03-05:09:21.859884 7             Options.atomic_flush: 0
2025/08/03-05:09:21.859886 7             Options.avoid_unnecessary_blocking_io: 0
2025/08/03-05:09:21.859888 7                 Options.persist_stats_to_disk: 0
2025/08/03-05:09:21.859889 7                 Options.write_dbid_to_manifest: 0
2025/08/03-05:09:21.859891 7                 Options.log_readahead_size: 0
2025/08/03-05:09:21.859893 7                 Options.file_checksum_gen_factory: Unknown
2025/08/03-05:09:21.859894 7                 Options.best_efforts_recovery: 0
2025/08/03-05:09:21.859896 7                Options.max_bgerror_resume_count: 2147483647
2025/08/03-05:09:21.859927 7            Options.bgerror_resume_retry_interval: 1000000
2025/08/03-05:09:21.859929 7             Options.allow_data_in_errors: 0
2025/08/03-05:09:21.859931 7             Options.db_host_id: __hostname__
2025/08/03-05:09:21.859939 7             Options.max_background_jobs: 1
2025/08/03-05:09:21.859941 7             Options.max_background_compactions: -1
2025/08/03-05:09:21.859942 7             Options.max_subcompactions: 1
2025/08/03-05:09:21.859944 7             Options.avoid_flush_during_shutdown: 0
2025/08/03-05:09:21.859945 7           Options.writable_file_max_buffer_size: 1048576
2025/08/03-05:09:21.859947 7             Options.delayed_write_rate : 16777216
2025/08/03-05:09:21.859948 7             Options.max_total_wal_size: 0
2025/08/03-05:09:21.859950 7             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/03-05:09:21.859951 7                   Options.stats_dump_period_sec: 600
2025/08/03-05:09:21.859953 7                 Options.stats_persist_period_sec: 600
2025/08/03-05:09:21.859954 7                 Options.stats_history_buffer_size: 1048576
2025/08/03-05:09:21.859956 7                          Options.max_open_files: -1
2025/08/03-05:09:21.859958 7                          Options.bytes_per_sync: 0
2025/08/03-05:09:21.859959 7                      Options.wal_bytes_per_sync: 0
2025/08/03-05:09:21.859961 7                   Options.strict_bytes_per_sync: 0
2025/08/03-05:09:21.859963 7       Options.compaction_readahead_size: 0
2025/08/03-05:09:21.859964 7                  Options.max_background_flushes: 1
2025/08/03-05:09:21.859966 7 Compression algorithms supported:
2025/08/03-05:09:21.859969 7 	kZSTD supported: 1
2025/08/03-05:09:21.859972 7 	kXpressCompression supported: 0
2025/08/03-05:09:21.859974 7 	kBZip2Compression supported: 0
2025/08/03-05:09:21.859976 7 	kZSTDNotFinalCompression supported: 1
2025/08/03-05:09:21.859978 7 	kLZ4Compression supported: 0
2025/08/03-05:09:21.859979 7 	kZlibCompression supported: 0
2025/08/03-05:09:21.859981 7 	kLZ4HCCompression supported: 0
2025/08/03-05:09:21.859983 7 	kSnappyCompression supported: 0
2025/08/03-05:09:21.859995 7 Fast CRC32 supported: Not supported on x86
2025/08/03-05:09:21.874787 7 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000004
2025/08/03-05:09:21.877852 7 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/03-05:09:21.878683 7               Options.comparator: leveldb.BytewiseComparator
2025/08/03-05:09:21.878694 7           Options.merge_operator: None
2025/08/03-05:09:21.878696 7        Options.compaction_filter: None
2025/08/03-05:09:21.878698 7        Options.compaction_filter_factory: None
2025/08/03-05:09:21.878700 7  Options.sst_partitioner_factory: None
2025/08/03-05:09:21.878702 7         Options.memtable_factory: SkipListFactory
2025/08/03-05:09:21.878704 7            Options.table_factory: BlockBasedTable
2025/08/03-05:09:21.878791 7            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f13afcc90c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f13aff74070
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/03-05:09:21.878805 7        Options.write_buffer_size: 67108864
2025/08/03-05:09:21.878808 7  Options.max_write_buffer_number: 2
2025/08/03-05:09:21.878813 7        Options.compression[0]: NoCompression
2025/08/03-05:09:21.878815 7        Options.compression[1]: NoCompression
2025/08/03-05:09:21.878817 7        Options.compression[2]: ZSTD
2025/08/03-05:09:21.878818 7        Options.compression[3]: ZSTD
2025/08/03-05:09:21.878820 7        Options.compression[4]: ZSTD
2025/08/03-05:09:21.878822 7                  Options.bottommost_compression: Disabled
2025/08/03-05:09:21.878824 7       Options.prefix_extractor: nullptr
2025/08/03-05:09:21.878826 7   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/03-05:09:21.878827 7             Options.num_levels: 5
2025/08/03-05:09:21.878829 7        Options.min_write_buffer_number_to_merge: 1
2025/08/03-05:09:21.878831 7     Options.max_write_buffer_number_to_maintain: 0
2025/08/03-05:09:21.878832 7     Options.max_write_buffer_size_to_maintain: 0
2025/08/03-05:09:21.878834 7            Options.bottommost_compression_opts.window_bits: -14
2025/08/03-05:09:21.878836 7                  Options.bottommost_compression_opts.level: 32767
2025/08/03-05:09:21.878838 7               Options.bottommost_compression_opts.strategy: 0
2025/08/03-05:09:21.878839 7         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/03-05:09:21.878841 7         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/03-05:09:21.878843 7         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/03-05:09:21.878844 7                  Options.bottommost_compression_opts.enabled: false
2025/08/03-05:09:21.878913 7         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/03-05:09:21.878981 7            Options.compression_opts.window_bits: -14
2025/08/03-05:09:21.878985 7                  Options.compression_opts.level: 32767
2025/08/03-05:09:21.878987 7               Options.compression_opts.strategy: 0
2025/08/03-05:09:21.878989 7         Options.compression_opts.max_dict_bytes: 0
2025/08/03-05:09:21.878990 7         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/03-05:09:21.878992 7         Options.compression_opts.parallel_threads: 1
2025/08/03-05:09:21.878993 7                  Options.compression_opts.enabled: false
2025/08/03-05:09:21.879286 7         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/03-05:09:21.879293 7      Options.level0_file_num_compaction_trigger: 4
2025/08/03-05:09:21.879296 7          Options.level0_slowdown_writes_trigger: 20
2025/08/03-05:09:21.879297 7              Options.level0_stop_writes_trigger: 36
2025/08/03-05:09:21.879299 7                   Options.target_file_size_base: 67108864
2025/08/03-05:09:21.879301 7             Options.target_file_size_multiplier: 2
2025/08/03-05:09:21.879303 7                Options.max_bytes_for_level_base: 268435456
2025/08/03-05:09:21.879304 7 Options.level_compaction_dynamic_level_bytes: 0
2025/08/03-05:09:21.879306 7          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/03-05:09:21.879312 7 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/03-05:09:21.879314 7 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/03-05:09:21.879317 7 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/03-05:09:21.879318 7 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/03-05:09:21.879320 7 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/03-05:09:21.879321 7 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/03-05:09:21.879322 7 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/03-05:09:21.879323 7       Options.max_sequential_skip_in_iterations: 8
2025/08/03-05:09:21.879324 7                    Options.max_compaction_bytes: 1677721600
2025/08/03-05:09:21.879325 7                        Options.arena_block_size: 1048576
2025/08/03-05:09:21.879326 7   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/03-05:09:21.879327 7   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/03-05:09:21.879328 7       Options.rate_limit_delay_max_milliseconds: 100
2025/08/03-05:09:21.879329 7                Options.disable_auto_compactions: 0
2025/08/03-05:09:21.879334 7                        Options.compaction_style: kCompactionStyleLevel
2025/08/03-05:09:21.879336 7                          Options.compaction_pri: kMinOverlappingRatio
2025/08/03-05:09:21.879337 7 Options.compaction_options_universal.size_ratio: 1
2025/08/03-05:09:21.879338 7 Options.compaction_options_universal.min_merge_width: 2
2025/08/03-05:09:21.879339 7 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/03-05:09:21.879340 7 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/03-05:09:21.879341 7 Options.compaction_options_universal.compression_size_percent: -1
2025/08/03-05:09:21.879343 7 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/03-05:09:21.879344 7 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/03-05:09:21.879345 7 Options.compaction_options_fifo.allow_compaction: 0
2025/08/03-05:09:21.879410 7                   Options.table_properties_collectors: 
2025/08/03-05:09:21.879413 7                   Options.inplace_update_support: 0
2025/08/03-05:09:21.879414 7                 Options.inplace_update_num_locks: 10000
2025/08/03-05:09:21.879415 7               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/03-05:09:21.879418 7               Options.memtable_whole_key_filtering: 0
2025/08/03-05:09:21.879420 7   Options.memtable_huge_page_size: 0
2025/08/03-05:09:21.879422 7                           Options.bloom_locality: 0
2025/08/03-05:09:21.879423 7                    Options.max_successive_merges: 0
2025/08/03-05:09:21.879425 7                Options.optimize_filters_for_hits: 0
2025/08/03-05:09:21.879427 7                Options.paranoid_file_checks: 0
2025/08/03-05:09:21.879428 7                Options.force_consistency_checks: 1
2025/08/03-05:09:21.879430 7                Options.report_bg_io_stats: 0
2025/08/03-05:09:21.879432 7                               Options.ttl: 2592000
2025/08/03-05:09:21.879433 7          Options.periodic_compaction_seconds: 0
2025/08/03-05:09:21.879435 7                       Options.enable_blob_files: false
2025/08/03-05:09:21.879437 7                           Options.min_blob_size: 0
2025/08/03-05:09:21.879643 7                          Options.blob_file_size: 268435456
2025/08/03-05:09:21.879649 7                   Options.blob_compression_type: NoCompression
2025/08/03-05:09:21.879651 7          Options.enable_blob_garbage_collection: false
2025/08/03-05:09:21.879653 7      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/03-05:09:21.879813 7 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/03-05:09:21.879818 7          Options.blob_compaction_readahead_size: 0
2025/08/03-05:09:21.891957 7 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000004 succeeded,manifest_file_number is 4, next_file_number is 6, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/03-05:09:21.891982 7 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/08/03-05:09:21.909997 7 [db/version_set.cc:4409] Creating manifest 8
2025/08/03-05:09:21.971875 7 EVENT_LOG_v1 {"time_micros": 1754197761971699, "job": 1, "event": "recovery_started", "wal_files": [5]}
2025/08/03-05:09:21.971896 7 [db/db_impl/db_impl_open.cc:888] Recovering log #5 mode 2
2025/08/03-05:09:22.010463 7 EVENT_LOG_v1 {"time_micros": 1754197762010374, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 9, "file_size": 1733, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 766, "index_size": 49, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 1402, "raw_average_key_size": 41, "raw_value_size": 187, "raw_average_value_size": 5, "num_data_blocks": 1, "num_entries": 34, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1754197761, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "03159e3e-3b7d-4577-9841-3dc735330813", "db_session_id": "0IG7HY073V2R4E4TSRW7", "orig_file_number": 9}}
2025/08/03-05:09:22.011815 7 [db/version_set.cc:4409] Creating manifest 10
2025/08/03-05:09:22.059688 7 EVENT_LOG_v1 {"time_micros": 1754197762059651, "job": 1, "event": "recovery_finished"}
2025/08/03-05:09:22.120386 7 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000005.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/08/03-05:09:22.120674 7 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f13afa70000
2025/08/03-05:09:22.122976 7 DB pointer 0x7f1373f31c00
2025/08/03-05:09:22.129624 53 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/03-05:09:22.129652 53 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.3 total, 0.3 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.69 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.024       0      0       0.0       0.0
 Sum      1/0    1.69 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.024       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.024       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.02              0.00         1    0.024       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.3 total, 0.3 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f13aff74070#7 capacity: 512.00 MB collections: 1 last_copies: 0 last_secs: 0.000192 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
