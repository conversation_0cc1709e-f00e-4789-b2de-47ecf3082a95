2025/08/03-04:58:32.429491 27 RocksDB version: 6.29.5
2025/08/03-04:58:32.430091 27 Git sha 0
2025/08/03-04:58:32.430101 27 Compile date 2024-11-15 11:22:58
2025/08/03-04:58:32.430105 27 DB SUMMARY
2025/08/03-04:58:32.430107 27 DB Session ID:  9D3JZJM2N1TJAE1R6YE0
2025/08/03-04:58:32.433565 27 SST files in /var/lib/milvus/rdb_data dir, Total Num: 0, files: 
2025/08/03-04:58:32.433587 27 Write Ahead Log file in /var/lib/milvus/rdb_data: 
2025/08/03-04:58:32.433593 27                         Options.error_if_exists: 0
2025/08/03-04:58:32.433595 27                       Options.create_if_missing: 1
2025/08/03-04:58:32.433597 27                         Options.paranoid_checks: 1
2025/08/03-04:58:32.433599 27             Options.flush_verify_memtable_count: 1
2025/08/03-04:58:32.433600 27                               Options.track_and_verify_wals_in_manifest: 0
2025/08/03-04:58:32.433601 27                                     Options.env: 0x7fcc7a8bad00
2025/08/03-04:58:32.433604 27                                      Options.fs: PosixFileSystem
2025/08/03-04:58:32.433606 27                                Options.info_log: 0x7fcbe4a80140
2025/08/03-04:58:32.433608 27                Options.max_file_opening_threads: 16
2025/08/03-04:58:32.433609 27                              Options.statistics: (nil)
2025/08/03-04:58:32.433611 27                               Options.use_fsync: 0
2025/08/03-04:58:32.433613 27                       Options.max_log_file_size: 0
2025/08/03-04:58:32.433615 27                  Options.max_manifest_file_size: 1073741824
2025/08/03-04:58:32.433617 27                   Options.log_file_time_to_roll: 0
2025/08/03-04:58:32.433618 27                       Options.keep_log_file_num: 1000
2025/08/03-04:58:32.433619 27                    Options.recycle_log_file_num: 0
2025/08/03-04:58:32.433621 27                         Options.allow_fallocate: 1
2025/08/03-04:58:32.433622 27                        Options.allow_mmap_reads: 0
2025/08/03-04:58:32.433624 27                       Options.allow_mmap_writes: 0
2025/08/03-04:58:32.433626 27                        Options.use_direct_reads: 0
2025/08/03-04:58:32.433627 27                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/03-04:58:32.433629 27          Options.create_missing_column_families: 1
2025/08/03-04:58:32.433632 27                              Options.db_log_dir: 
2025/08/03-04:58:32.433633 27                                 Options.wal_dir: 
2025/08/03-04:58:32.433635 27                Options.table_cache_numshardbits: 6
2025/08/03-04:58:32.433636 27                         Options.WAL_ttl_seconds: 0
2025/08/03-04:58:32.433638 27                       Options.WAL_size_limit_MB: 0
2025/08/03-04:58:32.433640 27                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/03-04:58:32.433641 27             Options.manifest_preallocation_size: 4194304
2025/08/03-04:58:32.433643 27                     Options.is_fd_close_on_exec: 1
2025/08/03-04:58:32.433644 27                   Options.advise_random_on_open: 1
2025/08/03-04:58:32.433646 27                   Options.experimental_mempurge_threshold: 0.000000
2025/08/03-04:58:32.433653 27                    Options.db_write_buffer_size: 0
2025/08/03-04:58:32.433654 27                    Options.write_buffer_manager: 0x7fcbe5c40280
2025/08/03-04:58:32.433656 27         Options.access_hint_on_compaction_start: 1
2025/08/03-04:58:32.433657 27  Options.new_table_reader_for_compaction_inputs: 0
2025/08/03-04:58:32.433659 27           Options.random_access_max_buffer_size: 1048576
2025/08/03-04:58:32.433660 27                      Options.use_adaptive_mutex: 0
2025/08/03-04:58:32.433662 27                            Options.rate_limiter: (nil)
2025/08/03-04:58:32.433666 27     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/03-04:58:32.433668 27                       Options.wal_recovery_mode: 2
2025/08/03-04:58:32.433669 27                  Options.enable_thread_tracking: 0
2025/08/03-04:58:32.433671 27                  Options.enable_pipelined_write: 0
2025/08/03-04:58:32.433673 27                  Options.unordered_write: 0
2025/08/03-04:58:32.434219 27         Options.allow_concurrent_memtable_write: 1
2025/08/03-04:58:32.434232 27      Options.enable_write_thread_adaptive_yield: 1
2025/08/03-04:58:32.434234 27             Options.write_thread_max_yield_usec: 100
2025/08/03-04:58:32.434236 27            Options.write_thread_slow_yield_usec: 3
2025/08/03-04:58:32.434238 27                               Options.row_cache: None
2025/08/03-04:58:32.434240 27                              Options.wal_filter: None
2025/08/03-04:58:32.434242 27             Options.avoid_flush_during_recovery: 0
2025/08/03-04:58:32.434244 27             Options.allow_ingest_behind: 0
2025/08/03-04:58:32.434246 27             Options.preserve_deletes: 0
2025/08/03-04:58:32.434247 27             Options.two_write_queues: 0
2025/08/03-04:58:32.434249 27             Options.manual_wal_flush: 0
2025/08/03-04:58:32.434250 27             Options.atomic_flush: 0
2025/08/03-04:58:32.434252 27             Options.avoid_unnecessary_blocking_io: 0
2025/08/03-04:58:32.434253 27                 Options.persist_stats_to_disk: 0
2025/08/03-04:58:32.434254 27                 Options.write_dbid_to_manifest: 0
2025/08/03-04:58:32.434256 27                 Options.log_readahead_size: 0
2025/08/03-04:58:32.434258 27                 Options.file_checksum_gen_factory: Unknown
2025/08/03-04:58:32.434260 27                 Options.best_efforts_recovery: 0
2025/08/03-04:58:32.434262 27                Options.max_bgerror_resume_count: 2147483647
2025/08/03-04:58:32.434263 27            Options.bgerror_resume_retry_interval: 1000000
2025/08/03-04:58:32.434265 27             Options.allow_data_in_errors: 0
2025/08/03-04:58:32.434267 27             Options.db_host_id: __hostname__
2025/08/03-04:58:32.434269 27             Options.max_background_jobs: 1
2025/08/03-04:58:32.434271 27             Options.max_background_compactions: -1
2025/08/03-04:58:32.434273 27             Options.max_subcompactions: 1
2025/08/03-04:58:32.434274 27             Options.avoid_flush_during_shutdown: 0
2025/08/03-04:58:32.434276 27           Options.writable_file_max_buffer_size: 1048576
2025/08/03-04:58:32.434277 27             Options.delayed_write_rate : 16777216
2025/08/03-04:58:32.434279 27             Options.max_total_wal_size: 0
2025/08/03-04:58:32.434281 27             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/03-04:58:32.434283 27                   Options.stats_dump_period_sec: 600
2025/08/03-04:58:32.434284 27                 Options.stats_persist_period_sec: 600
2025/08/03-04:58:32.434286 27                 Options.stats_history_buffer_size: 1048576
2025/08/03-04:58:32.434288 27                          Options.max_open_files: -1
2025/08/03-04:58:32.434289 27                          Options.bytes_per_sync: 0
2025/08/03-04:58:32.434291 27                      Options.wal_bytes_per_sync: 0
2025/08/03-04:58:32.434293 27                   Options.strict_bytes_per_sync: 0
2025/08/03-04:58:32.434295 27       Options.compaction_readahead_size: 0
2025/08/03-04:58:32.434296 27                  Options.max_background_flushes: 1
2025/08/03-04:58:32.434298 27 Compression algorithms supported:
2025/08/03-04:58:32.434302 27 	kZSTD supported: 1
2025/08/03-04:58:32.434304 27 	kXpressCompression supported: 0
2025/08/03-04:58:32.434306 27 	kBZip2Compression supported: 0
2025/08/03-04:58:32.434308 27 	kZSTDNotFinalCompression supported: 1
2025/08/03-04:58:32.434310 27 	kLZ4Compression supported: 0
2025/08/03-04:58:32.434312 27 	kZlibCompression supported: 0
2025/08/03-04:58:32.434314 27 	kLZ4HCCompression supported: 0
2025/08/03-04:58:32.434316 27 	kSnappyCompression supported: 0
2025/08/03-04:58:32.434327 27 Fast CRC32 supported: Not supported on x86
2025/08/03-04:58:32.451807 27 [db/db_impl/db_impl_open.cc:307] Creating manifest 1 
2025/08/03-04:58:32.474609 27 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000001
2025/08/03-04:58:32.476618 27 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/03-04:58:32.476633 27               Options.comparator: leveldb.BytewiseComparator
2025/08/03-04:58:32.476950 27           Options.merge_operator: None
2025/08/03-04:58:32.476960 27        Options.compaction_filter: None
2025/08/03-04:58:32.476962 27        Options.compaction_filter_factory: None
2025/08/03-04:58:32.476963 27  Options.sst_partitioner_factory: None
2025/08/03-04:58:32.476966 27         Options.memtable_factory: SkipListFactory
2025/08/03-04:58:32.476968 27            Options.table_factory: BlockBasedTable
2025/08/03-04:58:32.477007 27            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fcbe5d01260)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fcbe5c40010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/03-04:58:32.477010 27        Options.write_buffer_size: 67108864
2025/08/03-04:58:32.477011 27  Options.max_write_buffer_number: 2
2025/08/03-04:58:32.477014 27        Options.compression[0]: NoCompression
2025/08/03-04:58:32.477016 27        Options.compression[1]: NoCompression
2025/08/03-04:58:32.477018 27        Options.compression[2]: ZSTD
2025/08/03-04:58:32.477020 27        Options.compression[3]: ZSTD
2025/08/03-04:58:32.477022 27        Options.compression[4]: ZSTD
2025/08/03-04:58:32.477023 27                  Options.bottommost_compression: Disabled
2025/08/03-04:58:32.477025 27       Options.prefix_extractor: nullptr
2025/08/03-04:58:32.477026 27   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/03-04:58:32.477028 27             Options.num_levels: 5
2025/08/03-04:58:32.477029 27        Options.min_write_buffer_number_to_merge: 1
2025/08/03-04:58:32.477031 27     Options.max_write_buffer_number_to_maintain: 0
2025/08/03-04:58:32.477032 27     Options.max_write_buffer_size_to_maintain: 0
2025/08/03-04:58:32.477034 27            Options.bottommost_compression_opts.window_bits: -14
2025/08/03-04:58:32.477036 27                  Options.bottommost_compression_opts.level: 32767
2025/08/03-04:58:32.477038 27               Options.bottommost_compression_opts.strategy: 0
2025/08/03-04:58:32.477039 27         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/03-04:58:32.477041 27         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/03-04:58:32.477042 27         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/03-04:58:32.477044 27                  Options.bottommost_compression_opts.enabled: false
2025/08/03-04:58:32.477046 27         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/03-04:58:32.477048 27            Options.compression_opts.window_bits: -14
2025/08/03-04:58:32.477049 27                  Options.compression_opts.level: 32767
2025/08/03-04:58:32.477051 27               Options.compression_opts.strategy: 0
2025/08/03-04:58:32.477053 27         Options.compression_opts.max_dict_bytes: 0
2025/08/03-04:58:32.477054 27         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/03-04:58:32.477056 27         Options.compression_opts.parallel_threads: 1
2025/08/03-04:58:32.477057 27                  Options.compression_opts.enabled: false
2025/08/03-04:58:32.477059 27         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/03-04:58:32.477280 27      Options.level0_file_num_compaction_trigger: 4
2025/08/03-04:58:32.477288 27          Options.level0_slowdown_writes_trigger: 20
2025/08/03-04:58:32.477289 27              Options.level0_stop_writes_trigger: 36
2025/08/03-04:58:32.477291 27                   Options.target_file_size_base: 67108864
2025/08/03-04:58:32.477293 27             Options.target_file_size_multiplier: 2
2025/08/03-04:58:32.477294 27                Options.max_bytes_for_level_base: 268435456
2025/08/03-04:58:32.477296 27 Options.level_compaction_dynamic_level_bytes: 0
2025/08/03-04:58:32.477298 27          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/03-04:58:32.477303 27 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/03-04:58:32.477306 27 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/03-04:58:32.477307 27 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/03-04:58:32.477309 27 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/03-04:58:32.477310 27 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/03-04:58:32.477312 27 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/03-04:58:32.477314 27 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/03-04:58:32.477315 27       Options.max_sequential_skip_in_iterations: 8
2025/08/03-04:58:32.477317 27                    Options.max_compaction_bytes: 1677721600
2025/08/03-04:58:32.477319 27                        Options.arena_block_size: 1048576
2025/08/03-04:58:32.477321 27   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/03-04:58:32.477322 27   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/03-04:58:32.477324 27       Options.rate_limit_delay_max_milliseconds: 100
2025/08/03-04:58:32.477326 27                Options.disable_auto_compactions: 0
2025/08/03-04:58:32.477333 27                        Options.compaction_style: kCompactionStyleLevel
2025/08/03-04:58:32.477335 27                          Options.compaction_pri: kMinOverlappingRatio
2025/08/03-04:58:32.477337 27 Options.compaction_options_universal.size_ratio: 1
2025/08/03-04:58:32.477339 27 Options.compaction_options_universal.min_merge_width: 2
2025/08/03-04:58:32.477341 27 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/03-04:58:32.477344 27 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/03-04:58:32.477346 27 Options.compaction_options_universal.compression_size_percent: -1
2025/08/03-04:58:32.477348 27 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/03-04:58:32.477349 27 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/03-04:58:32.477351 27 Options.compaction_options_fifo.allow_compaction: 0
2025/08/03-04:58:32.477363 27                   Options.table_properties_collectors: 
2025/08/03-04:58:32.477365 27                   Options.inplace_update_support: 0
2025/08/03-04:58:32.477367 27                 Options.inplace_update_num_locks: 10000
2025/08/03-04:58:32.477368 27               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/03-04:58:32.477371 27               Options.memtable_whole_key_filtering: 0
2025/08/03-04:58:32.477373 27   Options.memtable_huge_page_size: 0
2025/08/03-04:58:32.477374 27                           Options.bloom_locality: 0
2025/08/03-04:58:32.477376 27                    Options.max_successive_merges: 0
2025/08/03-04:58:32.477378 27                Options.optimize_filters_for_hits: 0
2025/08/03-04:58:32.477379 27                Options.paranoid_file_checks: 0
2025/08/03-04:58:32.477381 27                Options.force_consistency_checks: 1
2025/08/03-04:58:32.477382 27                Options.report_bg_io_stats: 0
2025/08/03-04:58:32.477384 27                               Options.ttl: 2592000
2025/08/03-04:58:32.477386 27          Options.periodic_compaction_seconds: 0
2025/08/03-04:58:32.477388 27                       Options.enable_blob_files: false
2025/08/03-04:58:32.477390 27                           Options.min_blob_size: 0
2025/08/03-04:58:32.477715 27                          Options.blob_file_size: 268435456
2025/08/03-04:58:32.477724 27                   Options.blob_compression_type: NoCompression
2025/08/03-04:58:32.477726 27          Options.enable_blob_garbage_collection: false
2025/08/03-04:58:32.477728 27      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/03-04:58:32.477732 27 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/03-04:58:32.477734 27          Options.blob_compaction_readahead_size: 0
2025/08/03-04:58:32.484810 27 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/03-04:58:32.484828 27 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/08/03-04:58:32.490650 27 [db/version_set.cc:4409] Creating manifest 4
2025/08/03-04:58:32.512331 27 [db/column_family.cc:605] --------------- Options for column family [properties]:
2025/08/03-04:58:32.512368 27               Options.comparator: leveldb.BytewiseComparator
2025/08/03-04:58:32.512370 27           Options.merge_operator: None
2025/08/03-04:58:32.512372 27        Options.compaction_filter: None
2025/08/03-04:58:32.512373 27        Options.compaction_filter_factory: None
2025/08/03-04:58:32.512375 27  Options.sst_partitioner_factory: None
2025/08/03-04:58:32.512377 27         Options.memtable_factory: SkipListFactory
2025/08/03-04:58:32.512378 27            Options.table_factory: BlockBasedTable
2025/08/03-04:58:32.512413 27            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fcbe5d01260)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fcbe5c40010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/03-04:58:32.512416 27        Options.write_buffer_size: 67108864
2025/08/03-04:58:32.512418 27  Options.max_write_buffer_number: 2
2025/08/03-04:58:32.512421 27        Options.compression[0]: NoCompression
2025/08/03-04:58:32.512422 27        Options.compression[1]: NoCompression
2025/08/03-04:58:32.512424 27        Options.compression[2]: ZSTD
2025/08/03-04:58:32.512426 27        Options.compression[3]: ZSTD
2025/08/03-04:58:32.512427 27        Options.compression[4]: ZSTD
2025/08/03-04:58:32.512429 27                  Options.bottommost_compression: Disabled
2025/08/03-04:58:32.512430 27       Options.prefix_extractor: nullptr
2025/08/03-04:58:32.512432 27   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/03-04:58:32.512433 27             Options.num_levels: 5
2025/08/03-04:58:32.512435 27        Options.min_write_buffer_number_to_merge: 1
2025/08/03-04:58:32.512436 27     Options.max_write_buffer_number_to_maintain: 0
2025/08/03-04:58:32.512438 27     Options.max_write_buffer_size_to_maintain: 0
2025/08/03-04:58:32.512439 27            Options.bottommost_compression_opts.window_bits: -14
2025/08/03-04:58:32.512441 27                  Options.bottommost_compression_opts.level: 32767
2025/08/03-04:58:32.512442 27               Options.bottommost_compression_opts.strategy: 0
2025/08/03-04:58:32.512444 27         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/03-04:58:32.512445 27         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/03-04:58:32.512447 27         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/03-04:58:32.512448 27                  Options.bottommost_compression_opts.enabled: false
2025/08/03-04:58:32.512450 27         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/03-04:58:32.512452 27            Options.compression_opts.window_bits: -14
2025/08/03-04:58:32.512454 27                  Options.compression_opts.level: 32767
2025/08/03-04:58:32.512455 27               Options.compression_opts.strategy: 0
2025/08/03-04:58:32.512457 27         Options.compression_opts.max_dict_bytes: 0
2025/08/03-04:58:32.512458 27         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/03-04:58:32.512460 27         Options.compression_opts.parallel_threads: 1
2025/08/03-04:58:32.513024 27                  Options.compression_opts.enabled: false
2025/08/03-04:58:32.513070 27         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/03-04:58:32.513075 27      Options.level0_file_num_compaction_trigger: 4
2025/08/03-04:58:32.513078 27          Options.level0_slowdown_writes_trigger: 20
2025/08/03-04:58:32.513080 27              Options.level0_stop_writes_trigger: 36
2025/08/03-04:58:32.513083 27                   Options.target_file_size_base: 67108864
2025/08/03-04:58:32.513086 27             Options.target_file_size_multiplier: 2
2025/08/03-04:58:32.513088 27                Options.max_bytes_for_level_base: 268435456
2025/08/03-04:58:32.513090 27 Options.level_compaction_dynamic_level_bytes: 0
2025/08/03-04:58:32.513093 27          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/03-04:58:32.513116 27 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/03-04:58:32.513120 27 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/03-04:58:32.513122 27 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/03-04:58:32.513124 27 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/03-04:58:32.513125 27 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/03-04:58:32.513127 27 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/03-04:58:32.513128 27 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/03-04:58:32.513130 27       Options.max_sequential_skip_in_iterations: 8
2025/08/03-04:58:32.513133 27                    Options.max_compaction_bytes: 1677721600
2025/08/03-04:58:32.513135 27                        Options.arena_block_size: 1048576
2025/08/03-04:58:32.513137 27   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/03-04:58:32.513138 27   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/03-04:58:32.513140 27       Options.rate_limit_delay_max_milliseconds: 100
2025/08/03-04:58:32.513142 27                Options.disable_auto_compactions: 0
2025/08/03-04:58:32.513184 27                        Options.compaction_style: kCompactionStyleLevel
2025/08/03-04:58:32.513190 27                          Options.compaction_pri: kMinOverlappingRatio
2025/08/03-04:58:32.513192 27 Options.compaction_options_universal.size_ratio: 1
2025/08/03-04:58:32.513194 27 Options.compaction_options_universal.min_merge_width: 2
2025/08/03-04:58:32.513196 27 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/03-04:58:32.513197 27 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/03-04:58:32.513199 27 Options.compaction_options_universal.compression_size_percent: -1
2025/08/03-04:58:32.513205 27 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/03-04:58:32.513207 27 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/03-04:58:32.513209 27 Options.compaction_options_fifo.allow_compaction: 0
2025/08/03-04:58:32.513264 27                   Options.table_properties_collectors: 
2025/08/03-04:58:32.513266 27                   Options.inplace_update_support: 0
2025/08/03-04:58:32.513268 27                 Options.inplace_update_num_locks: 10000
2025/08/03-04:58:32.513271 27               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/03-04:58:32.513275 27               Options.memtable_whole_key_filtering: 0
2025/08/03-04:58:32.513277 27   Options.memtable_huge_page_size: 0
2025/08/03-04:58:32.513278 27                           Options.bloom_locality: 0
2025/08/03-04:58:32.513280 27                    Options.max_successive_merges: 0
2025/08/03-04:58:32.513284 27                Options.optimize_filters_for_hits: 0
2025/08/03-04:58:32.513287 27                Options.paranoid_file_checks: 0
2025/08/03-04:58:32.513288 27                Options.force_consistency_checks: 1
2025/08/03-04:58:32.513290 27                Options.report_bg_io_stats: 0
2025/08/03-04:58:32.513292 27                               Options.ttl: 2592000
2025/08/03-04:58:32.513294 27          Options.periodic_compaction_seconds: 0
2025/08/03-04:58:32.514282 27                       Options.enable_blob_files: false
2025/08/03-04:58:32.514293 27                           Options.min_blob_size: 0
2025/08/03-04:58:32.514295 27                          Options.blob_file_size: 268435456
2025/08/03-04:58:32.514300 27                   Options.blob_compression_type: NoCompression
2025/08/03-04:58:32.514302 27          Options.enable_blob_garbage_collection: false
2025/08/03-04:58:32.514304 27      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/03-04:58:32.514309 27 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/03-04:58:32.514313 27          Options.blob_compaction_readahead_size: 0
2025/08/03-04:58:32.515790 27 [db/db_impl/db_impl.cc:2780] Created column family [properties] (ID 1)
2025/08/03-04:58:32.562323 27 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fcbe4b70700
2025/08/03-04:58:32.564413 27 DB pointer 0x7fcbe4a11c00
2025/08/03-04:58:35.565895 55 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/03-04:58:35.566244 55 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.1 total, 3.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.1 total, 3.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fcbe5c40010#8 capacity: 512.00 MB collections: 1 last_copies: 2 last_secs: 0.000154 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.1 total, 3.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fcbe5c40010#8 capacity: 512.00 MB collections: 1 last_copies: 2 last_secs: 0.000154 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/08/03-05:08:23.272779 667 [db/db_impl/db_impl.cc:481] Shutdown: canceling all background work
2025/08/03-05:08:23.283037 667 [db/db_impl/db_impl.cc:699] Shutdown complete
