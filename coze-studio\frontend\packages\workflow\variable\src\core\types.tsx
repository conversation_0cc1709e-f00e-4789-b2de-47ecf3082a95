/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { type BaseVariableField } from '@flowgram-adapter/free-layout-editor';
import { type FlowNodeEntity } from '@flowgram-adapter/free-layout-editor';
import { type ViewVariableMeta } from '@coze-workflow/base/types';

export enum ExtendASTKind {
  Image = 'Image',
  File = 'File',
  ExtendBaseType = 'ExtendBaseType',
  MergeGroupExpression = 'MergeGroupExpression',
  SyncBackOutputs = 'SyncBackOutputs',
}

export type WorkflowVariableField = BaseVariableField<
  Partial<ViewVariableMeta>
>;

export interface RenameInfo {
  prevKeyPath: string[];
  nextKeyPath: string[];

  // The location of the rename, and the corresponding key value
  modifyIndex: number;
  modifyKey: string;
}

export interface GetKeyPathCtx {
  // The current node
  node?: FlowNodeEntity;
  // Verify that the variable is in scope
  checkScope?: boolean;
}
