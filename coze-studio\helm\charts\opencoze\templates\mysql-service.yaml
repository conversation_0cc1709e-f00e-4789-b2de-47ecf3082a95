apiVersion: v1
kind: Service
metadata:
  name: {{ include "opencoze.fullname" . }}-mysql
  labels:
    {{- include "opencoze.labels" . | nindent 4 }}
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.mysql.port }}
      targetPort: {{ .Values.mysql.targetPort }}
      name: mysql
  selector:
    app.kubernetes.io/component: mysql
    app.kubernetes.io/name: {{ include "opencoze.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}