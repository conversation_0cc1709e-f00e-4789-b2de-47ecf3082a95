2025/08/03-04:58:32.245006 27 RocksDB version: 6.29.5
2025/08/03-04:58:32.246878 27 Git sha 0
2025/08/03-04:58:32.246891 27 Compile date 2024-11-15 11:22:58
2025/08/03-04:58:32.246906 27 DB SUMMARY
2025/08/03-04:58:32.246908 27 DB Session ID:  9D3JZJM2N1TJAE1R6YE1
2025/08/03-04:58:32.249022 27 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 0, files: 
2025/08/03-04:58:32.249044 27 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 
2025/08/03-04:58:32.249051 27                         Options.error_if_exists: 0
2025/08/03-04:58:32.249053 27                       Options.create_if_missing: 1
2025/08/03-04:58:32.249055 27                         Options.paranoid_checks: 1
2025/08/03-04:58:32.249057 27             Options.flush_verify_memtable_count: 1
2025/08/03-04:58:32.249059 27                               Options.track_and_verify_wals_in_manifest: 0
2025/08/03-04:58:32.249061 27                                     Options.env: 0x7fcc7a8bad00
2025/08/03-04:58:32.249064 27                                      Options.fs: PosixFileSystem
2025/08/03-04:58:32.249066 27                                Options.info_log: 0x7fcbe4a80050
2025/08/03-04:58:32.249068 27                Options.max_file_opening_threads: 16
2025/08/03-04:58:32.249070 27                              Options.statistics: (nil)
2025/08/03-04:58:32.249072 27                               Options.use_fsync: 0
2025/08/03-04:58:32.249091 27                       Options.max_log_file_size: 0
2025/08/03-04:58:32.249104 27                  Options.max_manifest_file_size: 1073741824
2025/08/03-04:58:32.249107 27                   Options.log_file_time_to_roll: 0
2025/08/03-04:58:32.249109 27                       Options.keep_log_file_num: 1000
2025/08/03-04:58:32.249111 27                    Options.recycle_log_file_num: 0
2025/08/03-04:58:32.249113 27                         Options.allow_fallocate: 1
2025/08/03-04:58:32.249114 27                        Options.allow_mmap_reads: 0
2025/08/03-04:58:32.249116 27                       Options.allow_mmap_writes: 0
2025/08/03-04:58:32.249118 27                        Options.use_direct_reads: 0
2025/08/03-04:58:32.249119 27                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/03-04:58:32.249121 27          Options.create_missing_column_families: 0
2025/08/03-04:58:32.249123 27                              Options.db_log_dir: 
2025/08/03-04:58:32.249125 27                                 Options.wal_dir: 
2025/08/03-04:58:32.249126 27                Options.table_cache_numshardbits: 6
2025/08/03-04:58:32.249128 27                         Options.WAL_ttl_seconds: 0
2025/08/03-04:58:32.249130 27                       Options.WAL_size_limit_MB: 0
2025/08/03-04:58:32.249132 27                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/03-04:58:32.249133 27             Options.manifest_preallocation_size: 4194304
2025/08/03-04:58:32.249135 27                     Options.is_fd_close_on_exec: 1
2025/08/03-04:58:32.249136 27                   Options.advise_random_on_open: 1
2025/08/03-04:58:32.249138 27                   Options.experimental_mempurge_threshold: 0.000000
2025/08/03-04:58:32.249587 27                    Options.db_write_buffer_size: 0
2025/08/03-04:58:32.249591 27                    Options.write_buffer_manager: 0x7fcbe5c400a0
2025/08/03-04:58:32.249594 27         Options.access_hint_on_compaction_start: 1
2025/08/03-04:58:32.249595 27  Options.new_table_reader_for_compaction_inputs: 0
2025/08/03-04:58:32.249596 27           Options.random_access_max_buffer_size: 1048576
2025/08/03-04:58:32.249597 27                      Options.use_adaptive_mutex: 0
2025/08/03-04:58:32.249598 27                            Options.rate_limiter: (nil)
2025/08/03-04:58:32.249605 27     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/03-04:58:32.249606 27                       Options.wal_recovery_mode: 2
2025/08/03-04:58:32.249607 27                  Options.enable_thread_tracking: 0
2025/08/03-04:58:32.249608 27                  Options.enable_pipelined_write: 0
2025/08/03-04:58:32.249609 27                  Options.unordered_write: 0
2025/08/03-04:58:32.250216 27         Options.allow_concurrent_memtable_write: 1
2025/08/03-04:58:32.250222 27      Options.enable_write_thread_adaptive_yield: 1
2025/08/03-04:58:32.250224 27             Options.write_thread_max_yield_usec: 100
2025/08/03-04:58:32.250226 27            Options.write_thread_slow_yield_usec: 3
2025/08/03-04:58:32.250227 27                               Options.row_cache: None
2025/08/03-04:58:32.250229 27                              Options.wal_filter: None
2025/08/03-04:58:32.250231 27             Options.avoid_flush_during_recovery: 0
2025/08/03-04:58:32.250233 27             Options.allow_ingest_behind: 0
2025/08/03-04:58:32.250234 27             Options.preserve_deletes: 0
2025/08/03-04:58:32.250236 27             Options.two_write_queues: 0
2025/08/03-04:58:32.250237 27             Options.manual_wal_flush: 0
2025/08/03-04:58:32.250239 27             Options.atomic_flush: 0
2025/08/03-04:58:32.250241 27             Options.avoid_unnecessary_blocking_io: 0
2025/08/03-04:58:32.250242 27                 Options.persist_stats_to_disk: 0
2025/08/03-04:58:32.250244 27                 Options.write_dbid_to_manifest: 0
2025/08/03-04:58:32.250245 27                 Options.log_readahead_size: 0
2025/08/03-04:58:32.250247 27                 Options.file_checksum_gen_factory: Unknown
2025/08/03-04:58:32.250249 27                 Options.best_efforts_recovery: 0
2025/08/03-04:58:32.250250 27                Options.max_bgerror_resume_count: 2147483647
2025/08/03-04:58:32.250252 27            Options.bgerror_resume_retry_interval: 1000000
2025/08/03-04:58:32.250254 27             Options.allow_data_in_errors: 0
2025/08/03-04:58:32.250255 27             Options.db_host_id: __hostname__
2025/08/03-04:58:32.250281 27             Options.max_background_jobs: 1
2025/08/03-04:58:32.250284 27             Options.max_background_compactions: -1
2025/08/03-04:58:32.250286 27             Options.max_subcompactions: 1
2025/08/03-04:58:32.250287 27             Options.avoid_flush_during_shutdown: 0
2025/08/03-04:58:32.250289 27           Options.writable_file_max_buffer_size: 1048576
2025/08/03-04:58:32.250290 27             Options.delayed_write_rate : 16777216
2025/08/03-04:58:32.250292 27             Options.max_total_wal_size: 0
2025/08/03-04:58:32.250293 27             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/03-04:58:32.250295 27                   Options.stats_dump_period_sec: 600
2025/08/03-04:58:32.250296 27                 Options.stats_persist_period_sec: 600
2025/08/03-04:58:32.250298 27                 Options.stats_history_buffer_size: 1048576
2025/08/03-04:58:32.250299 27                          Options.max_open_files: -1
2025/08/03-04:58:32.250300 27                          Options.bytes_per_sync: 0
2025/08/03-04:58:32.250302 27                      Options.wal_bytes_per_sync: 0
2025/08/03-04:58:32.250303 27                   Options.strict_bytes_per_sync: 0
2025/08/03-04:58:32.250305 27       Options.compaction_readahead_size: 0
2025/08/03-04:58:32.250306 27                  Options.max_background_flushes: 1
2025/08/03-04:58:32.250308 27 Compression algorithms supported:
2025/08/03-04:58:32.250312 27 	kZSTD supported: 1
2025/08/03-04:58:32.250314 27 	kXpressCompression supported: 0
2025/08/03-04:58:32.250317 27 	kBZip2Compression supported: 0
2025/08/03-04:58:32.250319 27 	kZSTDNotFinalCompression supported: 1
2025/08/03-04:58:32.250321 27 	kLZ4Compression supported: 0
2025/08/03-04:58:32.250323 27 	kZlibCompression supported: 0
2025/08/03-04:58:32.250324 27 	kLZ4HCCompression supported: 0
2025/08/03-04:58:32.250326 27 	kSnappyCompression supported: 0
2025/08/03-04:58:32.250335 27 Fast CRC32 supported: Not supported on x86
2025/08/03-04:58:32.269853 27 [db/db_impl/db_impl_open.cc:307] Creating manifest 1 
2025/08/03-04:58:32.293183 27 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000001
2025/08/03-04:58:32.296002 27 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/03-04:58:32.296301 27               Options.comparator: leveldb.BytewiseComparator
2025/08/03-04:58:32.296310 27           Options.merge_operator: None
2025/08/03-04:58:32.296312 27        Options.compaction_filter: None
2025/08/03-04:58:32.296314 27        Options.compaction_filter_factory: None
2025/08/03-04:58:32.296315 27  Options.sst_partitioner_factory: None
2025/08/03-04:58:32.296318 27         Options.memtable_factory: SkipListFactory
2025/08/03-04:58:32.296320 27            Options.table_factory: BlockBasedTable
2025/08/03-04:58:32.296401 27            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7fcbe5d000c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7fcbe5c40010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/03-04:58:32.296406 27        Options.write_buffer_size: 67108864
2025/08/03-04:58:32.296408 27  Options.max_write_buffer_number: 2
2025/08/03-04:58:32.296413 27        Options.compression[0]: NoCompression
2025/08/03-04:58:32.296415 27        Options.compression[1]: NoCompression
2025/08/03-04:58:32.296417 27        Options.compression[2]: ZSTD
2025/08/03-04:58:32.296420 27        Options.compression[3]: ZSTD
2025/08/03-04:58:32.296422 27        Options.compression[4]: ZSTD
2025/08/03-04:58:32.296423 27                  Options.bottommost_compression: Disabled
2025/08/03-04:58:32.296426 27       Options.prefix_extractor: nullptr
2025/08/03-04:58:32.296427 27   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/03-04:58:32.296429 27             Options.num_levels: 5
2025/08/03-04:58:32.296431 27        Options.min_write_buffer_number_to_merge: 1
2025/08/03-04:58:32.296433 27     Options.max_write_buffer_number_to_maintain: 0
2025/08/03-04:58:32.296434 27     Options.max_write_buffer_size_to_maintain: 0
2025/08/03-04:58:32.296436 27            Options.bottommost_compression_opts.window_bits: -14
2025/08/03-04:58:32.296438 27                  Options.bottommost_compression_opts.level: 32767
2025/08/03-04:58:32.296439 27               Options.bottommost_compression_opts.strategy: 0
2025/08/03-04:58:32.296441 27         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/03-04:58:32.296443 27         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/03-04:58:32.296444 27         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/03-04:58:32.296446 27                  Options.bottommost_compression_opts.enabled: false
2025/08/03-04:58:32.296448 27         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/03-04:58:32.296450 27            Options.compression_opts.window_bits: -14
2025/08/03-04:58:32.296452 27                  Options.compression_opts.level: 32767
2025/08/03-04:58:32.296453 27               Options.compression_opts.strategy: 0
2025/08/03-04:58:32.296455 27         Options.compression_opts.max_dict_bytes: 0
2025/08/03-04:58:32.296457 27         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/03-04:58:32.296459 27         Options.compression_opts.parallel_threads: 1
2025/08/03-04:58:32.296461 27                  Options.compression_opts.enabled: false
2025/08/03-04:58:32.296767 27         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/03-04:58:32.296776 27      Options.level0_file_num_compaction_trigger: 4
2025/08/03-04:58:32.296778 27          Options.level0_slowdown_writes_trigger: 20
2025/08/03-04:58:32.296780 27              Options.level0_stop_writes_trigger: 36
2025/08/03-04:58:32.296782 27                   Options.target_file_size_base: 67108864
2025/08/03-04:58:32.296784 27             Options.target_file_size_multiplier: 2
2025/08/03-04:58:32.296786 27                Options.max_bytes_for_level_base: 268435456
2025/08/03-04:58:32.296788 27 Options.level_compaction_dynamic_level_bytes: 0
2025/08/03-04:58:32.296790 27          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/03-04:58:32.296795 27 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/03-04:58:32.296798 27 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/03-04:58:32.296799 27 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/03-04:58:32.296801 27 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/03-04:58:32.296803 27 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/03-04:58:32.296805 27 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/03-04:58:32.296807 27 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/03-04:58:32.296809 27       Options.max_sequential_skip_in_iterations: 8
2025/08/03-04:58:32.296811 27                    Options.max_compaction_bytes: 1677721600
2025/08/03-04:58:32.296813 27                        Options.arena_block_size: 1048576
2025/08/03-04:58:32.296815 27   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/03-04:58:32.296817 27   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/03-04:58:32.296819 27       Options.rate_limit_delay_max_milliseconds: 100
2025/08/03-04:58:32.296820 27                Options.disable_auto_compactions: 0
2025/08/03-04:58:32.296826 27                        Options.compaction_style: kCompactionStyleLevel
2025/08/03-04:58:32.296829 27                          Options.compaction_pri: kMinOverlappingRatio
2025/08/03-04:58:32.296831 27 Options.compaction_options_universal.size_ratio: 1
2025/08/03-04:58:32.296832 27 Options.compaction_options_universal.min_merge_width: 2
2025/08/03-04:58:32.296834 27 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/03-04:58:32.296836 27 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/03-04:58:32.296838 27 Options.compaction_options_universal.compression_size_percent: -1
2025/08/03-04:58:32.296840 27 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/03-04:58:32.296841 27 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/03-04:58:32.296843 27 Options.compaction_options_fifo.allow_compaction: 0
2025/08/03-04:58:32.296856 27                   Options.table_properties_collectors: 
2025/08/03-04:58:32.296858 27                   Options.inplace_update_support: 0
2025/08/03-04:58:32.296859 27                 Options.inplace_update_num_locks: 10000
2025/08/03-04:58:32.296861 27               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/03-04:58:32.296863 27               Options.memtable_whole_key_filtering: 0
2025/08/03-04:58:32.296866 27   Options.memtable_huge_page_size: 0
2025/08/03-04:58:32.296868 27                           Options.bloom_locality: 0
2025/08/03-04:58:32.296879 27                    Options.max_successive_merges: 0
2025/08/03-04:58:32.296881 27                Options.optimize_filters_for_hits: 0
2025/08/03-04:58:32.296883 27                Options.paranoid_file_checks: 0
2025/08/03-04:58:32.296885 27                Options.force_consistency_checks: 1
2025/08/03-04:58:32.296886 27                Options.report_bg_io_stats: 0
2025/08/03-04:58:32.296888 27                               Options.ttl: 2592000
2025/08/03-04:58:32.296890 27          Options.periodic_compaction_seconds: 0
2025/08/03-04:58:32.296891 27                       Options.enable_blob_files: false
2025/08/03-04:58:32.297152 27                           Options.min_blob_size: 0
2025/08/03-04:58:32.297157 27                          Options.blob_file_size: 268435456
2025/08/03-04:58:32.297160 27                   Options.blob_compression_type: NoCompression
2025/08/03-04:58:32.297162 27          Options.enable_blob_garbage_collection: false
2025/08/03-04:58:32.297164 27      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/03-04:58:32.297167 27 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/03-04:58:32.297169 27          Options.blob_compaction_readahead_size: 0
2025/08/03-04:58:32.318751 27 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/08/03-04:58:32.318772 27 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/08/03-04:58:32.349602 27 [db/version_set.cc:4409] Creating manifest 4
2025/08/03-04:58:32.420269 27 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7fcbe4b70000
2025/08/03-04:58:32.422320 27 DB pointer 0x7fcbe4a10000
2025/08/03-04:58:32.424330 55 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/03-04:58:32.424397 55 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.1 total, 0.1 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7fcbe5c40010#8 capacity: 512.00 MB collections: 1 last_copies: 0 last_secs: 0.000154 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/08/03-05:08:23.252351 667 [db/db_impl/db_impl.cc:481] Shutdown: canceling all background work
2025/08/03-05:08:23.268631 667 [db/db_impl/db_impl.cc:699] Shutdown complete
