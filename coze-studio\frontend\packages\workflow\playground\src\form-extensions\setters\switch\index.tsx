/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import React, { useCallback, useMemo } from 'react';

import { Switch as UISwitch } from '@coze-arch/coze-design';
import { type SetterComponentProps } from '@flowgram-adapter/free-layout-editor';

type SwitchProps = SetterComponentProps;

const Switch = ({ value, onChange, options, readonly }: SwitchProps) => {
  const { size = 'default', style = {} } = options;

  const onValueChange = useCallback((checked: boolean) => {
    onChange(checked);
  }, []);

  const memoStyle = useMemo(
    () => ({ ...style, verticalAlign: 'bottom' }),
    [style],
  );

  return (
    <UISwitch
      disabled={readonly}
      size={size}
      checked={value}
      style={memoStyle}
      onChange={onValueChange}
    />
  );
};

export const switchSetter = {
  key: 'Switch',
  component: Switch,
};
