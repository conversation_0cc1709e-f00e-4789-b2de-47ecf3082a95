/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import {
  DEFAULT_AVATAR_LANGUAGES,
  DEFAULT_IDE_PYTHON_CODE_PARAMS,
  DEFAULT_LANGUAGES,
  DEFAULT_OPEN_SOURCE_LANGUAGES,
  DEFAULT_TYPESCRIPT_CODE_PARAMS,
} from './constants';

function getLanguageTemplates(options?: { isBindDouyin?: boolean }) {
  // open source version only support Python(limit from backend)
  return IS_OPEN_SOURCE
    ? DEFAULT_OPEN_SOURCE_LANGUAGES
    : options?.isBindDouyin
    ? DEFAULT_AVATAR_LANGUAGES
    : DEFAULT_LANGUAGES;
}

function getDefaultValue(options?: { isBindDouyin?: boolean }) {
  const templates = getLanguageTemplates(options);

  if (templates[0]?.language === 'python') {
    return DEFAULT_IDE_PYTHON_CODE_PARAMS;
  }

  return DEFAULT_TYPESCRIPT_CODE_PARAMS;
}

export { getLanguageTemplates, getDefaultValue };
