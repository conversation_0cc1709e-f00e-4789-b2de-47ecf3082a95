/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { ProblemEmpty } from '../problem-panel/empty';
import { type WorkflowProblem, type ProblemItem } from '../../types';
import { MyProblemGroup } from './my-problem-group';

import css from './problem-group.module.less';

interface ProblemGroupProps {
  myProblems?: WorkflowProblem;
  otherProblems: WorkflowProblem[];
  onScroll: (p: ProblemItem) => void;
  onJump: (p: ProblemItem, workflowId: string) => void;
}

export const ProblemGroup: React.FC<ProblemGroupProps> = ({
  myProblems,
  otherProblems,
  onScroll,
  onJump,
}) => {
  const isEmpty = !myProblems && !otherProblems.length;

  if (isEmpty) {
    return <ProblemEmpty />;
  }

  return (
    <div className={css['problem-group']}>
      {myProblems ? (
        <MyProblemGroup
          problems={myProblems}
          showTitle={!!otherProblems.length}
          isMine
          onClick={onScroll}
        />
      ) : null}
      {otherProblems.map(other => (
        <MyProblemGroup
          problems={other}
          showTitle={true}
          onClick={p => onJump(p, other.workflowId)}
        />
      ))}
    </div>
  );
};
