/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { type SetterComponentProps } from '@flowgram-adapter/free-layout-editor';
import { useNodeTestId } from '@coze-workflow/base';

import { feedbackStatus2ValidateStatus } from '../components/utils';

// eslint-disable-next-line @typescript-eslint/naming-convention
export function toStandardSetter(Setter) {
  // Fix an issue where expressions passed to setters do not respond
  const ObserverSetter = Setter;
  return function StandardSetter(props: SetterComponentProps) {
    const {
      value,
      onChange,
      readonly = false,
      options,
      feedbackStatus,
    } = props;

    const { getNodeSetterId } = useNodeTestId();
    const setterTestId = getNodeSetterId('');
    const validateStatus = feedbackStatus2ValidateStatus(feedbackStatus);

    return (
      <ObserverSetter
        value={value}
        onChange={onChange}
        {...options}
        readonly={readonly || options.readonly}
        testId={setterTestId}
        validateStatus={validateStatus}
      />
    );
  };
}
