/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import React from 'react';

import classNames from 'classnames';
import { I18n } from '@coze-arch/i18n';

import styles from './index.module.less';

const Block = [
  { label: I18n.t('workflow_detail_condition_reference') },
  { label: I18n.t('workflow_detail_condition_select') },
  { label: I18n.t('workflow_detail_condition_comparison') },
];

export interface ConditionParamsHeaderProps {
  className?: string;
  style?: React.CSSProperties;
}

export default function ConditionParamsHeader({
  className,
  style,
}: ConditionParamsHeaderProps) {
  return (
    <div className={classNames(styles.container, className)} style={style}>
      {Block.map((item, index) => (
        <div key={index} className={styles.block}>
          {item.label ? (
            <span className={styles.label}>{item.label}</span>
          ) : null}
        </div>
      ))}
    </div>
  );
}
