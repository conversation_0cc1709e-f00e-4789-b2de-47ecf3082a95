apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-server" (include "opencoze.fullname" .) }}
  labels:
    {{- include "opencoze.labels" . | nindent 4 }}
spec:
  type: {{ .Values.cozeServer.service.type }}
  ports:
  {{- range .Values.cozeServer.service.ports }}
    - name: {{ .name }}
      port: {{ .port }}
      targetPort: {{ .name }}
      protocol: TCP
  {{- end }}
  selector:
    app.kubernetes.io/name: {{ include "opencoze.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/component: server
