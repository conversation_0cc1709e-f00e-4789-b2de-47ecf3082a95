/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { type FC, useState } from 'react';

import { JSONEditor } from '../json-editor';
import type { TreeNodeCustomData } from '../custom-tree-node/type';

export const JSONEditorWithState: FC<{
  updateKey: number;
  uniqueId: string;
  onChange: (data: TreeNodeCustomData[]) => void;
  onClose: () => void;
}> = props => {
  const { updateKey, uniqueId, onClose, onChange } = props;

  const [JSONString, setJSONString] = useState('');

  return (
    <JSONEditor
      key={updateKey}
      id={uniqueId}
      value={JSONString}
      setValue={(value: string) => {
        setJSONString(value);
      }}
      onClose={onClose}
      onChange={onChange}
    />
  );
};
