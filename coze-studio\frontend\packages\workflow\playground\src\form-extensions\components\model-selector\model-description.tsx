/*
 * Copyright 2025 coze-dev Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { MdBoxLazy } from '@coze-arch/bot-md-box-adapter/lazy';
import { type ModelDescGroup } from '@coze-arch/bot-api/developer_api';

export const ModelDescription: React.FC<{
  descriptionGroupList: ModelDescGroup[];
}> = ({ descriptionGroupList }) => (
  <MdBoxLazy
    autoFixSyntax={{ autoFixEnding: false }}
    markDown={descriptionGroupList
      .map(({ group_name, desc }) => `${group_name}\n${desc?.join('\n')}`)
      .join('\n\n')}
  />
);
