2025/08/03-05:09:22.161511 7 RocksDB version: 6.29.5
2025/08/03-05:09:22.163590 7 Git sha 0
2025/08/03-05:09:22.163609 7 Compile date 2024-11-15 11:22:58
2025/08/03-05:09:22.163613 7 DB SUMMARY
2025/08/03-05:09:22.163615 7 DB Session ID:  0IG7HY073V2R4E4TSRW6
2025/08/03-05:09:22.170583 7 CURRENT file:  CURRENT
2025/08/03-05:09:22.170622 7 IDENTITY file:  IDENTITY
2025/08/03-05:09:22.172260 7 MANIFEST file:  MANIFEST-000004 size: 116 Bytes
2025/08/03-05:09:22.172283 7 SST files in /var/lib/milvus/rdb_data dir, Total Num: 0, files: 
2025/08/03-05:09:22.172307 7 Write Ahead Log file in /var/lib/milvus/rdb_data: 000005.log size: 0 ; 
2025/08/03-05:09:22.172312 7                         Options.error_if_exists: 0
2025/08/03-05:09:22.172314 7                       Options.create_if_missing: 1
2025/08/03-05:09:22.172316 7                         Options.paranoid_checks: 1
2025/08/03-05:09:22.172317 7             Options.flush_verify_memtable_count: 1
2025/08/03-05:09:22.172319 7                               Options.track_and_verify_wals_in_manifest: 0
2025/08/03-05:09:22.172320 7                                     Options.env: 0x7f13b8261d00
2025/08/03-05:09:22.172322 7                                      Options.fs: PosixFileSystem
2025/08/03-05:09:22.172324 7                                Options.info_log: 0x7f13afb8b090
2025/08/03-05:09:22.172325 7                Options.max_file_opening_threads: 16
2025/08/03-05:09:22.172327 7                              Options.statistics: (nil)
2025/08/03-05:09:22.172328 7                               Options.use_fsync: 0
2025/08/03-05:09:22.172330 7                       Options.max_log_file_size: 0
2025/08/03-05:09:22.172332 7                  Options.max_manifest_file_size: 1073741824
2025/08/03-05:09:22.172333 7                   Options.log_file_time_to_roll: 0
2025/08/03-05:09:22.172335 7                       Options.keep_log_file_num: 1000
2025/08/03-05:09:22.172337 7                    Options.recycle_log_file_num: 0
2025/08/03-05:09:22.172338 7                         Options.allow_fallocate: 1
2025/08/03-05:09:22.172340 7                        Options.allow_mmap_reads: 0
2025/08/03-05:09:22.172341 7                       Options.allow_mmap_writes: 0
2025/08/03-05:09:22.172343 7                        Options.use_direct_reads: 0
2025/08/03-05:09:22.172344 7                        Options.use_direct_io_for_flush_and_compaction: 0
2025/08/03-05:09:22.172346 7          Options.create_missing_column_families: 1
2025/08/03-05:09:22.172347 7                              Options.db_log_dir: 
2025/08/03-05:09:22.172349 7                                 Options.wal_dir: 
2025/08/03-05:09:22.172350 7                Options.table_cache_numshardbits: 6
2025/08/03-05:09:22.172351 7                         Options.WAL_ttl_seconds: 0
2025/08/03-05:09:22.172353 7                       Options.WAL_size_limit_MB: 0
2025/08/03-05:09:22.172354 7                        Options.max_write_batch_group_size_bytes: 1048576
2025/08/03-05:09:22.172356 7             Options.manifest_preallocation_size: 4194304
2025/08/03-05:09:22.172358 7                     Options.is_fd_close_on_exec: 1
2025/08/03-05:09:22.172359 7                   Options.advise_random_on_open: 1
2025/08/03-05:09:22.172361 7                   Options.experimental_mempurge_threshold: 0.000000
2025/08/03-05:09:22.172367 7                    Options.db_write_buffer_size: 0
2025/08/03-05:09:22.172369 7                    Options.write_buffer_manager: 0x7f13aff742e0
2025/08/03-05:09:22.172371 7         Options.access_hint_on_compaction_start: 1
2025/08/03-05:09:22.172372 7  Options.new_table_reader_for_compaction_inputs: 0
2025/08/03-05:09:22.172374 7           Options.random_access_max_buffer_size: 1048576
2025/08/03-05:09:22.172376 7                      Options.use_adaptive_mutex: 0
2025/08/03-05:09:22.172377 7                            Options.rate_limiter: (nil)
2025/08/03-05:09:22.172380 7     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/08/03-05:09:22.172382 7                       Options.wal_recovery_mode: 2
2025/08/03-05:09:22.172383 7                  Options.enable_thread_tracking: 0
2025/08/03-05:09:22.172940 7                  Options.enable_pipelined_write: 0
2025/08/03-05:09:22.172952 7                  Options.unordered_write: 0
2025/08/03-05:09:22.172954 7         Options.allow_concurrent_memtable_write: 1
2025/08/03-05:09:22.172955 7      Options.enable_write_thread_adaptive_yield: 1
2025/08/03-05:09:22.172957 7             Options.write_thread_max_yield_usec: 100
2025/08/03-05:09:22.172958 7            Options.write_thread_slow_yield_usec: 3
2025/08/03-05:09:22.172960 7                               Options.row_cache: None
2025/08/03-05:09:22.172962 7                              Options.wal_filter: None
2025/08/03-05:09:22.172965 7             Options.avoid_flush_during_recovery: 0
2025/08/03-05:09:22.172967 7             Options.allow_ingest_behind: 0
2025/08/03-05:09:22.172969 7             Options.preserve_deletes: 0
2025/08/03-05:09:22.173030 7             Options.two_write_queues: 0
2025/08/03-05:09:22.173035 7             Options.manual_wal_flush: 0
2025/08/03-05:09:22.173037 7             Options.atomic_flush: 0
2025/08/03-05:09:22.173039 7             Options.avoid_unnecessary_blocking_io: 0
2025/08/03-05:09:22.173041 7                 Options.persist_stats_to_disk: 0
2025/08/03-05:09:22.173042 7                 Options.write_dbid_to_manifest: 0
2025/08/03-05:09:22.173044 7                 Options.log_readahead_size: 0
2025/08/03-05:09:22.173046 7                 Options.file_checksum_gen_factory: Unknown
2025/08/03-05:09:22.173048 7                 Options.best_efforts_recovery: 0
2025/08/03-05:09:22.173050 7                Options.max_bgerror_resume_count: 2147483647
2025/08/03-05:09:22.173052 7            Options.bgerror_resume_retry_interval: 1000000
2025/08/03-05:09:22.173054 7             Options.allow_data_in_errors: 0
2025/08/03-05:09:22.173055 7             Options.db_host_id: __hostname__
2025/08/03-05:09:22.173058 7             Options.max_background_jobs: 1
2025/08/03-05:09:22.173059 7             Options.max_background_compactions: -1
2025/08/03-05:09:22.173061 7             Options.max_subcompactions: 1
2025/08/03-05:09:22.173063 7             Options.avoid_flush_during_shutdown: 0
2025/08/03-05:09:22.173065 7           Options.writable_file_max_buffer_size: 1048576
2025/08/03-05:09:22.173067 7             Options.delayed_write_rate : 16777216
2025/08/03-05:09:22.173069 7             Options.max_total_wal_size: 0
2025/08/03-05:09:22.173071 7             Options.delete_obsolete_files_period_micros: 21600000000
2025/08/03-05:09:22.173072 7                   Options.stats_dump_period_sec: 600
2025/08/03-05:09:22.173074 7                 Options.stats_persist_period_sec: 600
2025/08/03-05:09:22.173076 7                 Options.stats_history_buffer_size: 1048576
2025/08/03-05:09:22.173078 7                          Options.max_open_files: -1
2025/08/03-05:09:22.173080 7                          Options.bytes_per_sync: 0
2025/08/03-05:09:22.173081 7                      Options.wal_bytes_per_sync: 0
2025/08/03-05:09:22.173083 7                   Options.strict_bytes_per_sync: 0
2025/08/03-05:09:22.173085 7       Options.compaction_readahead_size: 0
2025/08/03-05:09:22.173087 7                  Options.max_background_flushes: 1
2025/08/03-05:09:22.173089 7 Compression algorithms supported:
2025/08/03-05:09:22.173093 7 	kZSTD supported: 1
2025/08/03-05:09:22.173095 7 	kXpressCompression supported: 0
2025/08/03-05:09:22.173097 7 	kBZip2Compression supported: 0
2025/08/03-05:09:22.173099 7 	kZSTDNotFinalCompression supported: 1
2025/08/03-05:09:22.173101 7 	kLZ4Compression supported: 0
2025/08/03-05:09:22.173102 7 	kZlibCompression supported: 0
2025/08/03-05:09:22.173104 7 	kLZ4HCCompression supported: 0
2025/08/03-05:09:22.173106 7 	kSnappyCompression supported: 0
2025/08/03-05:09:22.173115 7 Fast CRC32 supported: Not supported on x86
2025/08/03-05:09:22.184347 7 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000004
2025/08/03-05:09:22.188369 7 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/08/03-05:09:22.190070 7               Options.comparator: leveldb.BytewiseComparator
2025/08/03-05:09:22.190087 7           Options.merge_operator: None
2025/08/03-05:09:22.190089 7        Options.compaction_filter: None
2025/08/03-05:09:22.190091 7        Options.compaction_filter_factory: None
2025/08/03-05:09:22.190093 7  Options.sst_partitioner_factory: None
2025/08/03-05:09:22.190096 7         Options.memtable_factory: SkipListFactory
2025/08/03-05:09:22.190099 7            Options.table_factory: BlockBasedTable
2025/08/03-05:09:22.190144 7            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f13afcca380)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f13aff74070
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/03-05:09:22.190146 7        Options.write_buffer_size: 67108864
2025/08/03-05:09:22.190148 7  Options.max_write_buffer_number: 2
2025/08/03-05:09:22.190152 7        Options.compression[0]: NoCompression
2025/08/03-05:09:22.190154 7        Options.compression[1]: NoCompression
2025/08/03-05:09:22.190156 7        Options.compression[2]: ZSTD
2025/08/03-05:09:22.190158 7        Options.compression[3]: ZSTD
2025/08/03-05:09:22.190160 7        Options.compression[4]: ZSTD
2025/08/03-05:09:22.190162 7                  Options.bottommost_compression: Disabled
2025/08/03-05:09:22.190163 7       Options.prefix_extractor: nullptr
2025/08/03-05:09:22.190165 7   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/03-05:09:22.190167 7             Options.num_levels: 5
2025/08/03-05:09:22.190168 7        Options.min_write_buffer_number_to_merge: 1
2025/08/03-05:09:22.190170 7     Options.max_write_buffer_number_to_maintain: 0
2025/08/03-05:09:22.190172 7     Options.max_write_buffer_size_to_maintain: 0
2025/08/03-05:09:22.190174 7            Options.bottommost_compression_opts.window_bits: -14
2025/08/03-05:09:22.190175 7                  Options.bottommost_compression_opts.level: 32767
2025/08/03-05:09:22.190177 7               Options.bottommost_compression_opts.strategy: 0
2025/08/03-05:09:22.190179 7         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/03-05:09:22.190181 7         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/03-05:09:22.190183 7         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/03-05:09:22.190184 7                  Options.bottommost_compression_opts.enabled: false
2025/08/03-05:09:22.190186 7         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/03-05:09:22.190188 7            Options.compression_opts.window_bits: -14
2025/08/03-05:09:22.190190 7                  Options.compression_opts.level: 32767
2025/08/03-05:09:22.190192 7               Options.compression_opts.strategy: 0
2025/08/03-05:09:22.190193 7         Options.compression_opts.max_dict_bytes: 0
2025/08/03-05:09:22.190195 7         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/03-05:09:22.190196 7         Options.compression_opts.parallel_threads: 1
2025/08/03-05:09:22.190198 7                  Options.compression_opts.enabled: false
2025/08/03-05:09:22.190199 7         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/03-05:09:22.190628 7      Options.level0_file_num_compaction_trigger: 4
2025/08/03-05:09:22.190638 7          Options.level0_slowdown_writes_trigger: 20
2025/08/03-05:09:22.190641 7              Options.level0_stop_writes_trigger: 36
2025/08/03-05:09:22.190642 7                   Options.target_file_size_base: 67108864
2025/08/03-05:09:22.190645 7             Options.target_file_size_multiplier: 2
2025/08/03-05:09:22.190646 7                Options.max_bytes_for_level_base: 268435456
2025/08/03-05:09:22.190648 7 Options.level_compaction_dynamic_level_bytes: 0
2025/08/03-05:09:22.190650 7          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/03-05:09:22.190656 7 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/03-05:09:22.190659 7 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/03-05:09:22.190661 7 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/03-05:09:22.190662 7 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/03-05:09:22.190664 7 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/03-05:09:22.190666 7 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/03-05:09:22.190668 7 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/03-05:09:22.190670 7       Options.max_sequential_skip_in_iterations: 8
2025/08/03-05:09:22.190672 7                    Options.max_compaction_bytes: 1677721600
2025/08/03-05:09:22.190674 7                        Options.arena_block_size: 1048576
2025/08/03-05:09:22.190676 7   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/03-05:09:22.190678 7   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/03-05:09:22.190680 7       Options.rate_limit_delay_max_milliseconds: 100
2025/08/03-05:09:22.190682 7                Options.disable_auto_compactions: 0
2025/08/03-05:09:22.190688 7                        Options.compaction_style: kCompactionStyleLevel
2025/08/03-05:09:22.190691 7                          Options.compaction_pri: kMinOverlappingRatio
2025/08/03-05:09:22.190693 7 Options.compaction_options_universal.size_ratio: 1
2025/08/03-05:09:22.190695 7 Options.compaction_options_universal.min_merge_width: 2
2025/08/03-05:09:22.190696 7 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/03-05:09:22.190698 7 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/03-05:09:22.190700 7 Options.compaction_options_universal.compression_size_percent: -1
2025/08/03-05:09:22.190703 7 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/03-05:09:22.190705 7 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/03-05:09:22.190706 7 Options.compaction_options_fifo.allow_compaction: 0
2025/08/03-05:09:22.190719 7                   Options.table_properties_collectors: 
2025/08/03-05:09:22.190721 7                   Options.inplace_update_support: 0
2025/08/03-05:09:22.190723 7                 Options.inplace_update_num_locks: 10000
2025/08/03-05:09:22.190725 7               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/03-05:09:22.190727 7               Options.memtable_whole_key_filtering: 0
2025/08/03-05:09:22.190729 7   Options.memtable_huge_page_size: 0
2025/08/03-05:09:22.190731 7                           Options.bloom_locality: 0
2025/08/03-05:09:22.190733 7                    Options.max_successive_merges: 0
2025/08/03-05:09:22.190735 7                Options.optimize_filters_for_hits: 0
2025/08/03-05:09:22.190736 7                Options.paranoid_file_checks: 0
2025/08/03-05:09:22.190738 7                Options.force_consistency_checks: 1
2025/08/03-05:09:22.190740 7                Options.report_bg_io_stats: 0
2025/08/03-05:09:22.190741 7                               Options.ttl: 2592000
2025/08/03-05:09:22.190742 7          Options.periodic_compaction_seconds: 0
2025/08/03-05:09:22.190744 7                       Options.enable_blob_files: false
2025/08/03-05:09:22.190746 7                           Options.min_blob_size: 0
2025/08/03-05:09:22.191080 7                          Options.blob_file_size: 268435456
2025/08/03-05:09:22.191089 7                   Options.blob_compression_type: NoCompression
2025/08/03-05:09:22.191092 7          Options.enable_blob_garbage_collection: false
2025/08/03-05:09:22.191094 7      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/03-05:09:22.191098 7 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/03-05:09:22.191100 7          Options.blob_compaction_readahead_size: 0
2025/08/03-05:09:22.199592 7 [db/column_family.cc:605] --------------- Options for column family [properties]:
2025/08/03-05:09:22.199610 7               Options.comparator: leveldb.BytewiseComparator
2025/08/03-05:09:22.199612 7           Options.merge_operator: None
2025/08/03-05:09:22.199615 7        Options.compaction_filter: None
2025/08/03-05:09:22.199616 7        Options.compaction_filter_factory: None
2025/08/03-05:09:22.199618 7  Options.sst_partitioner_factory: None
2025/08/03-05:09:22.199620 7         Options.memtable_factory: SkipListFactory
2025/08/03-05:09:22.199621 7            Options.table_factory: BlockBasedTable
2025/08/03-05:09:22.199649 7            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x7f13afcca380)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0x7f13aff74070
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 536870912
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/08/03-05:09:22.199651 7        Options.write_buffer_size: 67108864
2025/08/03-05:09:22.199653 7  Options.max_write_buffer_number: 2
2025/08/03-05:09:22.199655 7        Options.compression[0]: NoCompression
2025/08/03-05:09:22.199678 7        Options.compression[1]: NoCompression
2025/08/03-05:09:22.199680 7        Options.compression[2]: ZSTD
2025/08/03-05:09:22.199682 7        Options.compression[3]: ZSTD
2025/08/03-05:09:22.199683 7        Options.compression[4]: ZSTD
2025/08/03-05:09:22.199684 7                  Options.bottommost_compression: Disabled
2025/08/03-05:09:22.199686 7       Options.prefix_extractor: nullptr
2025/08/03-05:09:22.199688 7   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/08/03-05:09:22.199689 7             Options.num_levels: 5
2025/08/03-05:09:22.199690 7        Options.min_write_buffer_number_to_merge: 1
2025/08/03-05:09:22.199692 7     Options.max_write_buffer_number_to_maintain: 0
2025/08/03-05:09:22.199693 7     Options.max_write_buffer_size_to_maintain: 0
2025/08/03-05:09:22.199695 7            Options.bottommost_compression_opts.window_bits: -14
2025/08/03-05:09:22.199696 7                  Options.bottommost_compression_opts.level: 32767
2025/08/03-05:09:22.199698 7               Options.bottommost_compression_opts.strategy: 0
2025/08/03-05:09:22.199699 7         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/08/03-05:09:22.199700 7         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/08/03-05:09:22.199702 7         Options.bottommost_compression_opts.parallel_threads: 1
2025/08/03-05:09:22.199703 7                  Options.bottommost_compression_opts.enabled: false
2025/08/03-05:09:22.199705 7         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/08/03-05:09:22.200111 7            Options.compression_opts.window_bits: -14
2025/08/03-05:09:22.200122 7                  Options.compression_opts.level: 32767
2025/08/03-05:09:22.200129 7               Options.compression_opts.strategy: 0
2025/08/03-05:09:22.200131 7         Options.compression_opts.max_dict_bytes: 0
2025/08/03-05:09:22.200132 7         Options.compression_opts.zstd_max_train_bytes: 0
2025/08/03-05:09:22.200137 7         Options.compression_opts.parallel_threads: 1
2025/08/03-05:09:22.200138 7                  Options.compression_opts.enabled: false
2025/08/03-05:09:22.200140 7         Options.compression_opts.max_dict_buffer_bytes: 0
2025/08/03-05:09:22.200142 7      Options.level0_file_num_compaction_trigger: 4
2025/08/03-05:09:22.200143 7          Options.level0_slowdown_writes_trigger: 20
2025/08/03-05:09:22.200145 7              Options.level0_stop_writes_trigger: 36
2025/08/03-05:09:22.200146 7                   Options.target_file_size_base: 67108864
2025/08/03-05:09:22.200148 7             Options.target_file_size_multiplier: 2
2025/08/03-05:09:22.200149 7                Options.max_bytes_for_level_base: 268435456
2025/08/03-05:09:22.200150 7 Options.level_compaction_dynamic_level_bytes: 0
2025/08/03-05:09:22.200152 7          Options.max_bytes_for_level_multiplier: 10.000000
2025/08/03-05:09:22.200157 7 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/08/03-05:09:22.200159 7 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/08/03-05:09:22.200160 7 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/08/03-05:09:22.200161 7 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/08/03-05:09:22.200162 7 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/08/03-05:09:22.200164 7 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/08/03-05:09:22.200165 7 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/08/03-05:09:22.200167 7       Options.max_sequential_skip_in_iterations: 8
2025/08/03-05:09:22.200168 7                    Options.max_compaction_bytes: 1677721600
2025/08/03-05:09:22.200170 7                        Options.arena_block_size: 1048576
2025/08/03-05:09:22.200171 7   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/08/03-05:09:22.200172 7   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/08/03-05:09:22.200174 7       Options.rate_limit_delay_max_milliseconds: 100
2025/08/03-05:09:22.200175 7                Options.disable_auto_compactions: 0
2025/08/03-05:09:22.200182 7                        Options.compaction_style: kCompactionStyleLevel
2025/08/03-05:09:22.200184 7                          Options.compaction_pri: kMinOverlappingRatio
2025/08/03-05:09:22.200186 7 Options.compaction_options_universal.size_ratio: 1
2025/08/03-05:09:22.200187 7 Options.compaction_options_universal.min_merge_width: 2
2025/08/03-05:09:22.200189 7 Options.compaction_options_universal.max_merge_width: 4294967295
2025/08/03-05:09:22.200191 7 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/08/03-05:09:22.200193 7 Options.compaction_options_universal.compression_size_percent: -1
2025/08/03-05:09:22.200198 7 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/08/03-05:09:22.200200 7 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/08/03-05:09:22.200201 7 Options.compaction_options_fifo.allow_compaction: 0
2025/08/03-05:09:22.200212 7                   Options.table_properties_collectors: 
2025/08/03-05:09:22.200213 7                   Options.inplace_update_support: 0
2025/08/03-05:09:22.200215 7                 Options.inplace_update_num_locks: 10000
2025/08/03-05:09:22.200216 7               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/08/03-05:09:22.200219 7               Options.memtable_whole_key_filtering: 0
2025/08/03-05:09:22.200220 7   Options.memtable_huge_page_size: 0
2025/08/03-05:09:22.200222 7                           Options.bloom_locality: 0
2025/08/03-05:09:22.200223 7                    Options.max_successive_merges: 0
2025/08/03-05:09:22.200606 7                Options.optimize_filters_for_hits: 0
2025/08/03-05:09:22.200615 7                Options.paranoid_file_checks: 0
2025/08/03-05:09:22.200616 7                Options.force_consistency_checks: 1
2025/08/03-05:09:22.200618 7                Options.report_bg_io_stats: 0
2025/08/03-05:09:22.200620 7                               Options.ttl: 2592000
2025/08/03-05:09:22.200621 7          Options.periodic_compaction_seconds: 0
2025/08/03-05:09:22.200623 7                       Options.enable_blob_files: false
2025/08/03-05:09:22.200625 7                           Options.min_blob_size: 0
2025/08/03-05:09:22.200627 7                          Options.blob_file_size: 268435456
2025/08/03-05:09:22.200631 7                   Options.blob_compression_type: NoCompression
2025/08/03-05:09:22.200633 7          Options.enable_blob_garbage_collection: false
2025/08/03-05:09:22.200634 7      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/08/03-05:09:22.200638 7 Options.blob_garbage_collection_force_threshold: 1.000000
2025/08/03-05:09:22.200641 7          Options.blob_compaction_readahead_size: 0
2025/08/03-05:09:22.212752 7 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000004 succeeded,manifest_file_number is 4, next_file_number is 7, last_sequence is 0, log_number is 5,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 0
2025/08/03-05:09:22.212771 7 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/08/03-05:09:22.212774 7 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 5
2025/08/03-05:09:22.221587 7 [db/version_set.cc:4409] Creating manifest 10
2025/08/03-05:09:22.266453 7 EVENT_LOG_v1 {"time_micros": 1754197762266437, "job": 1, "event": "recovery_started", "wal_files": [5]}
2025/08/03-05:09:22.266468 7 [db/db_impl/db_impl_open.cc:888] Recovering log #5 mode 2
2025/08/03-05:09:22.273809 7 [db/version_set.cc:4409] Creating manifest 11
2025/08/03-05:09:22.310208 7 EVENT_LOG_v1 {"time_micros": 1754197762310191, "job": 1, "event": "recovery_finished"}
2025/08/03-05:09:22.520445 7 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0x7f13afa70700
2025/08/03-05:09:22.549802 7 DB pointer 0x7f1373f33800
2025/08/03-05:09:25.565986 53 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/08/03-05:09:25.566017 53 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.4 total, 3.4 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.4 total, 3.4 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0x7f13aff74070#7 capacity: 512.00 MB collections: 1 last_copies: 2 last_secs: 0.000192 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blo