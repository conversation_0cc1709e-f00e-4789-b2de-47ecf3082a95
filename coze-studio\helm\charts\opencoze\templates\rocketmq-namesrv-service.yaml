{{- if and (eq (lower (default "rmq" .Values.cozeServer.env.COZE_MQ_TYPE)) "rmq") .Values.rocketmq.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "opencoze.fullname" . }}-rocketmq-namesrv
  labels:
    {{- include "opencoze.labels" . | nindent 4 }}
spec:
  clusterIP: None
  ports:
    - port: 9876
      name: namesrv
  selector:
    app.kubernetes.io/component: rocketmq-namesrv
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/name: {{ include "opencoze.name" . }}
{{- end }}